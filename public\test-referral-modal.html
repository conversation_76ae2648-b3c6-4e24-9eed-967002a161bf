<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Referral Modal - SkillsAssess</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Referral Modal Test</h1>
        
        <!-- Test Controls -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Controls</h2>
            
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium mb-2">1. Test Modal with Sample Data</h3>
                    <button id="test-modal-sample" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Show Modal with Sample Data
                    </button>
                </div>

                <div>
                    <h3 class="font-medium mb-2">2. Test Modal with Current User Data</h3>
                    <button id="test-modal-user" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Show Modal with User Data
                    </button>
                    <span id="user-status" class="ml-4 text-sm text-gray-600"></span>
                </div>

                <div>
                    <h3 class="font-medium mb-2">3. Test Modal with No Data</h3>
                    <button id="test-modal-empty" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                        Show Modal with No Data
                    </button>
                </div>

                <div>
                    <h3 class="font-medium mb-2">4. Test Responsive Design</h3>
                    <div class="space-x-2">
                        <button id="test-mobile" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                            Mobile View (375px)
                        </button>
                        <button id="test-tablet" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                            Tablet View (768px)
                        </button>
                        <button id="test-desktop" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                            Desktop View (1200px)
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="results-content" class="space-y-2">
                <p class="text-gray-600">Click the test buttons above to verify modal functionality...</p>
            </div>
        </div>
    </div>

    <!-- Include the referral modal files -->
    <link rel="stylesheet" href="referral-modal.css">
    <script src="referral-modal-new.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        // Sample data for testing
        const sampleUserData = {
            referralCode: 'SAMPLE123',
            referralStats: {
                successfulReferrals: 3,
                creditsEarned: 15
            }
        };

        // Test functions
        function addTestResult(message, type = 'info') {
            const resultsContent = document.getElementById('results-content');
            const p = document.createElement('p');
            p.className = type === 'success' ? 'text-green-600' : 
                         type === 'error' ? 'text-red-600' : 
                         type === 'warning' ? 'text-yellow-600' : 'text-gray-600';
            p.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsContent.appendChild(p);
            resultsContent.scrollTop = resultsContent.scrollHeight;
        }

        // Simple notification function for testing
        window.showNotification = function(message, type) {
            addTestResult(`Notification: ${message} (${type})`, type);
        };

        // Test event handlers
        document.getElementById('test-modal-sample').addEventListener('click', () => {
            addTestResult('Testing modal with sample data...');
            try {
                showReferralModal(sampleUserData);
                addTestResult('Modal opened successfully with sample data', 'success');
            } catch (error) {
                addTestResult(`Error opening modal: ${error.message}`, 'error');
            }
        });

        document.getElementById('test-modal-user').addEventListener('click', async () => {
            const user = firebase.auth().currentUser;
            if (!user) {
                addTestResult('No user authenticated - please log in first', 'warning');
                return;
            }

            addTestResult('Testing modal with current user data...');
            try {
                const userDoc = await db.collection('Admins').doc(user.email).get();
                const userData = userDoc.exists ? userDoc.data() : null;
                
                showReferralModal(userData);
                addTestResult('Modal opened successfully with user data', 'success');
            } catch (error) {
                addTestResult(`Error opening modal: ${error.message}`, 'error');
            }
        });

        document.getElementById('test-modal-empty').addEventListener('click', () => {
            addTestResult('Testing modal with no data...');
            try {
                showReferralModal(null);
                addTestResult('Modal opened successfully with no data', 'success');
            } catch (error) {
                addTestResult(`Error opening modal: ${error.message}`, 'error');
            }
        });

        // Responsive testing
        document.getElementById('test-mobile').addEventListener('click', () => {
            document.body.style.width = '375px';
            document.body.style.margin = '0 auto';
            addTestResult('Switched to mobile view (375px)', 'info');
        });

        document.getElementById('test-tablet').addEventListener('click', () => {
            document.body.style.width = '768px';
            document.body.style.margin = '0 auto';
            addTestResult('Switched to tablet view (768px)', 'info');
        });

        document.getElementById('test-desktop').addEventListener('click', () => {
            document.body.style.width = '100%';
            document.body.style.margin = '0';
            addTestResult('Switched to desktop view (100%)', 'info');
        });

        // Check authentication status
        firebase.auth().onAuthStateChanged((user) => {
            const userStatus = document.getElementById('user-status');
            if (user) {
                userStatus.textContent = `✓ Authenticated as: ${user.email}`;
                userStatus.className = 'ml-4 text-sm text-green-600';
            } else {
                userStatus.textContent = '✗ Not authenticated';
                userStatus.className = 'ml-4 text-sm text-red-600';
            }
        });

        // Test modal functionality on load
        window.addEventListener('load', () => {
            addTestResult('Page loaded, referral modal script ready');
            
            // Test if the modal function is available
            if (typeof showReferralModal === 'function') {
                addTestResult('✓ showReferralModal function is available', 'success');
            } else {
                addTestResult('✗ showReferralModal function not found', 'error');
            }
        });
    </script>
</body>
</html>
