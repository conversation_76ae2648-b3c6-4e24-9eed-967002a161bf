// Simple Referral Modal Implementation
// Uses standard HTML/CSS without complex DOM manipulation

(function() {
    'use strict';

    // Global function to show referral modal
    window.showReferralModal = function(userData) {
        // Remove any existing modal first
        const existingModal = document.getElementById('referral-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHTML = createReferralModalHTML(userData);
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Initialize modal functionality
        initializeReferralModal();
        
        // Show modal with animation
        setTimeout(() => {
            const modal = document.getElementById('referral-modal');
            if (modal) {
                modal.classList.add('show');
            }
        }, 10);
    };

    function createReferralModalHTML(userData) {
        const referralCode = userData?.referralCode || 'Loading...';
        const successfulReferrals = userData?.referralStats?.successfulReferrals || 0;
        const creditsEarned = userData?.referralStats?.creditsEarned || 0;
        
        return `
            <div id="referral-modal" class="referral-modal-backdrop">
                <div class="referral-modal-container">
                    <div class="referral-modal-header">
                        <div>
                            <h2 class="referral-modal-title">Referral Program</h2>
                            <p class="referral-modal-subtitle">Earn credits by inviting friends and colleagues</p>
                        </div>
                        <button class="referral-modal-close" onclick="closeReferralModal()">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="referral-modal-content">
                        <div class="referral-stats">
                            <div class="referral-stat-item">
                                <div class="referral-stat-number">${successfulReferrals}</div>
                                <div class="referral-stat-label">Successful Referrals</div>
                            </div>
                            <div class="referral-stat-item">
                                <div class="referral-stat-number">${creditsEarned}</div>
                                <div class="referral-stat-label">Credits Earned</div>
                            </div>
                        </div>
                        
                        <div class="referral-code-section">
                            <h3>Your Referral Code</h3>
                            <div class="referral-code-container">
                                <input type="text" id="referral-code-input" value="${referralCode}" readonly>
                                <button onclick="copyReferralCode()" class="referral-copy-btn">Copy</button>
                            </div>
                        </div>

                        <div class="referral-link-section">
                            <h3>Your Referral Link</h3>
                            <div class="referral-link-container">
                                <input type="text" id="referral-link-input" value="https://dashboard.skillsassess.ai/?ref=${referralCode}" readonly>
                                <button onclick="copyReferralLink()" class="referral-copy-btn">Copy Link</button>
                            </div>
                            <p class="referral-link-description">Share this direct link - your referral code is automatically applied when someone signs up!</p>
                        </div>
                        
                        <div class="referral-how-it-works">
                            <h3>How It Works</h3>
                            <div class="referral-steps">
                                <div class="referral-step">
                                    <div class="referral-step-number">1</div>
                                    <div class="referral-step-text">Share your referral code with friends</div>
                                </div>
                                <div class="referral-step">
                                    <div class="referral-step-number">2</div>
                                    <div class="referral-step-text">They sign up using your code</div>
                                </div>
                                <div class="referral-step">
                                    <div class="referral-step-number">3</div>
                                    <div class="referral-step-text">Both of you get 5 free credits!</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="referral-share-section">
                            <h3>Share Your Code</h3>
                            <div class="referral-share-buttons">
                                <button onclick="shareViaEmail()" class="referral-share-btn">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                    </svg>
                                    Email
                                </button>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function initializeReferralModal() {
        // Close modal when clicking backdrop
        const modal = document.getElementById('referral-modal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeReferralModal();
                }
            });
        }

        // Close modal on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeReferralModal();
            }
        });
    }

    // Global function to close modal
    window.closeReferralModal = function() {
        const modal = document.getElementById('referral-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    };

    // Global function to copy referral code
    window.copyReferralCode = function() {
        const input = document.getElementById('referral-code-input');
        if (input) {
            input.select();
            document.execCommand('copy');

            // Show feedback
            const btn = document.querySelector('.referral-copy-btn');
            if (btn) {
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.backgroundColor = '#10B981';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.backgroundColor = '';
                }, 2000);
            }
        }
    };

    // Global function to copy referral link
    window.copyReferralLink = function() {
        const input = document.getElementById('referral-link-input');
        if (input) {
            input.select();
            document.execCommand('copy');

            // Show feedback
            const btn = input.nextElementSibling;
            if (btn) {
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.backgroundColor = '#10B981';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.backgroundColor = '';
                }, 2000);
            }
        }
    };

    // Share functions
    window.shareViaEmail = function() {
        const code = document.getElementById('referral-code-input')?.value;
        const link = document.getElementById('referral-link-input')?.value;
        const subject = 'Join SkillsAssess with my referral link';
        const body = `Hi! I'd like to invite you to join SkillsAssess, a great platform for skills assessment.

Click this link to sign up and we'll both get 5 free credits: ${link}

Alternatively, you can use my referral code "${code}" when signing up at https://dashboard.skillsassess.ai/

Looking forward to having you on the platform!`;
        window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
    };



})();
