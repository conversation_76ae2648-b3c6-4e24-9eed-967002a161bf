// Simple Referral Modal Implementation
// Uses standard HTML/CSS without complex DOM manipulation

(function() {
    'use strict';

    // Global function to show referral modal
    window.showReferralModal = function(userData) {
        // Remove any existing modal first
        const existingModal = document.getElementById('referral-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHTML = createReferralModalHTML(userData);
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Initialize modal functionality
        initializeReferralModal();
        
        // Show modal with animation
        setTimeout(() => {
            const modal = document.getElementById('referral-modal');
            if (modal) {
                modal.classList.add('show');
            }
        }, 10);
    };

    function createReferralModalHTML(userData) {
        const referralCode = userData?.referralCode || 'Loading...';
        const successfulReferrals = userData?.referralStats?.successfulReferrals || 0;
        const creditsEarned = userData?.referralStats?.creditsEarned || 0;
        
        return `
            <div id="referral-modal" class="referral-modal-backdrop">
                <div class="referral-modal-container">
                    <div class="referral-modal-header">
                        <div>
                            <h2 class="referral-modal-title">Referral Program</h2>
                            <p class="referral-modal-subtitle">Earn credits by inviting friends and colleagues</p>
                        </div>
                        <button class="referral-modal-close" onclick="closeReferralModal()">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="referral-modal-content">
                        <div class="referral-stats">
                            <div class="referral-stat-item">
                                <div class="referral-stat-number">${successfulReferrals}</div>
                                <div class="referral-stat-label">Successful Referrals</div>
                            </div>
                            <div class="referral-stat-item">
                                <div class="referral-stat-number">${creditsEarned}</div>
                                <div class="referral-stat-label">Credits Earned</div>
                            </div>
                        </div>
                        
                        <div class="referral-code-section">
                            <h3>Your Referral Code</h3>
                            <div class="referral-code-container">
                                <input type="text" id="referral-code-input" value="${referralCode}" readonly>
                                <button onclick="copyReferralCode()" class="referral-copy-btn">Copy</button>
                            </div>
                        </div>
                        
                        <div class="referral-how-it-works">
                            <h3>How It Works</h3>
                            <div class="referral-steps">
                                <div class="referral-step">
                                    <div class="referral-step-number">1</div>
                                    <div class="referral-step-text">Share your referral code with friends</div>
                                </div>
                                <div class="referral-step">
                                    <div class="referral-step-number">2</div>
                                    <div class="referral-step-text">They sign up using your code</div>
                                </div>
                                <div class="referral-step">
                                    <div class="referral-step-number">3</div>
                                    <div class="referral-step-text">Both of you get 5 free credits!</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="referral-share-section">
                            <h3>Share Your Code</h3>
                            <div class="referral-share-buttons">
                                <button onclick="shareViaEmail()" class="referral-share-btn">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                    </svg>
                                    Email
                                </button>
                                <button onclick="shareViaWhatsApp()" class="referral-share-btn">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                    </svg>
                                    WhatsApp
                                </button>
                                <button onclick="shareViaLinkedIn()" class="referral-share-btn">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                    LinkedIn
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function initializeReferralModal() {
        // Close modal when clicking backdrop
        const modal = document.getElementById('referral-modal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeReferralModal();
                }
            });
        }

        // Close modal on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeReferralModal();
            }
        });
    }

    // Global function to close modal
    window.closeReferralModal = function() {
        const modal = document.getElementById('referral-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    };

    // Global function to copy referral code
    window.copyReferralCode = function() {
        const input = document.getElementById('referral-code-input');
        if (input) {
            input.select();
            document.execCommand('copy');
            
            // Show feedback
            const btn = document.querySelector('.referral-copy-btn');
            if (btn) {
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.backgroundColor = '#10B981';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.backgroundColor = '';
                }, 2000);
            }
        }
    };

    // Share functions
    window.shareViaEmail = function() {
        const code = document.getElementById('referral-code-input')?.value;
        const subject = 'Join SkillsAssess with my referral code';
        const body = `Hi! I'd like to invite you to join SkillsAssess, a great platform for skills assessment. Use my referral code "${code}" when you sign up and we'll both get 5 free credits! Sign up at: https://dashboard.skillsassess.ai/`;
        window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
    };

    window.shareViaWhatsApp = function() {
        const code = document.getElementById('referral-code-input')?.value;
        const message = `Hi! I'd like to invite you to join SkillsAssess. Use my referral code "${code}" when you sign up and we'll both get 5 free credits! Sign up at: https://dashboard.skillsassess.ai/`;
        window.open(`https://wa.me/?text=${encodeURIComponent(message)}`);
    };

    window.shareViaLinkedIn = function() {
        const url = 'https://dashboard.skillsassess.ai/';
        const title = 'Join me on SkillsAssess';
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`);
    };

})();
