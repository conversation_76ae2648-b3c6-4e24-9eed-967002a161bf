(function(global) {
    let isModalInitialized = false;
    let isClosing = false;
    let currentUserData = null;

    async function showReferralModal(userData = null) {
        try {
            if (userData) {
                currentUserData = userData;
            }

            if (isModalInitialized) {
                await resetAndShowModal();
                return;
            }

            isClosing = false;

            // Create modal structure using the same pattern as user profile modal
            const overlay = document.createElement('div');
            overlay.id = 'referral-modal-overlay';
            overlay.className = 'modal-overlay';
            overlay.style.opacity = '0';

            overlay.innerHTML = createModalHTML(currentUserData);
            document.body.appendChild(overlay);
            isModalInitialized = true;

            // Initialize event listeners
            initializeEventListeners(overlay);

            // Animate modal appearance
            requestAnimationFrame(() => {
                if (!isClosing) {
                    setTimeout(() => {
                        overlay.style.opacity = '1';
                        const modalContent = overlay.querySelector('.modal-content');
                        if (modalContent) {
                            modalContent.style.opacity = '1';
                            modalContent.style.transform = 'scale(1)';
                        }
                    }, 50);
                }
            });

            addStyles();
        } catch (error) {
            console.error('Error showing referral modal:', error);
            throw error;
        }
    }

    async function resetAndShowModal() {
        const overlay = document.getElementById('referral-modal-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new data
        overlay.innerHTML = createModalHTML(currentUserData);

        // Re-initialize event listeners
        initializeEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 50);
        });
    }

    function createModalHTML(userData) {
        const referralCode = userData?.referralCode || 'LOADING...';
        const referralStats = userData?.referralStats || {
            successfulReferrals: 0,
            creditsEarned: 0
        };

        return `
            <div class="modal-content">
                <div class="modal-header">
                    <div class="referral-modal-title-container">
                        <h2 class="modal-title">Referral Program</h2>
                        <p class="referral-modal-subtitle">Earn credits by inviting friends and colleagues</p>
                    </div>
                    <button id="close-referral-modal" class="close-modal-button">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <div class="referral-modal-body">
                    <!-- How it works section -->
                    <div class="referral-how-it-works">
                        <h3>How it works</h3>
                        <div class="referral-steps">
                            <div class="referral-step">
                                <div class="referral-step-number">1</div>
                                <div class="referral-step-content">
                                    <h4>Share your code</h4>
                                    <p>Send your unique referral code to friends and colleagues</p>
                                </div>
                            </div>
                            <div class="referral-step">
                                <div class="referral-step-number">2</div>
                                <div class="referral-step-content">
                                    <h4>They sign up</h4>
                                    <p>When they create an account using your code</p>
                                </div>
                            </div>
                            <div class="referral-step">
                                <div class="referral-step-number">3</div>
                                <div class="referral-step-content">
                                    <h4>You both earn credits</h4>
                                    <p>Both you and your friend receive 5 bonus credits</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Referral code section -->
                    <div class="referral-code-section">
                        <h3>Your referral code</h3>
                        <div class="referral-code-container">
                            <div class="referral-code-display">
                                <span id="referral-code-text" class="referral-code-text">${referralCode}</span>
                                <button id="copy-referral-code-btn" class="referral-copy-button" title="Copy to clipboard">
                                    <svg class="copy-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <svg class="check-icon hidden" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path d="M5 13l4 4L19 7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                            <p class="referral-code-help">Share this code with friends when they sign up</p>
                        </div>
                    </div>

                    <!-- Statistics section -->
                    <div class="referral-stats-section">
                        <h3>Your referral statistics</h3>
                        <div class="referral-stats-grid">
                            <div class="referral-stat-card">
                                <div class="referral-stat-number">${referralStats.successfulReferrals}</div>
                                <div class="referral-stat-label">Successful referrals</div>
                            </div>
                            <div class="referral-stat-card">
                                <div class="referral-stat-number">${referralStats.creditsEarned}</div>
                                <div class="referral-stat-label">Credits earned</div>
                            </div>
                        </div>
                    </div>

                    <!-- Sharing section -->
                    <div class="referral-sharing-section">
                        <h3>Share your code</h3>
                        <p class="referral-sharing-text">
                            Tell your friends about SkillsAssess and use your referral code <strong>${referralCode}</strong> 
                            to get 5 bonus credits each when they sign up!
                        </p>
                        <div class="referral-sharing-buttons">
                            <button id="share-email-btn" class="referral-share-button">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Share via Email
                            </button>
                            <button id="share-copy-link-btn" class="referral-share-button">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path d="M10 13a5 5 0 007.54.54l3-3a5 5 0 00-7.07-7.07l-1.72 1.71" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M14 11a5 5 0 00-7.54-.54l-3 3a5 5 0 007.07 7.07l1.71-1.71" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Copy Signup Link
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function initializeEventListeners(overlay) {
        // Close modal button
        const closeButton = overlay.querySelector('#close-referral-modal');
        if (closeButton) {
            closeButton.addEventListener('click', closeModal);
        }

        // Close on backdrop click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeModal();
            }
        });

        // Copy referral code button
        const copyButton = overlay.querySelector('#copy-referral-code-btn');
        if (copyButton) {
            copyButton.addEventListener('click', copyReferralCode);
        }

        // Share buttons
        const shareEmailBtn = overlay.querySelector('#share-email-btn');
        if (shareEmailBtn) {
            shareEmailBtn.addEventListener('click', shareViaEmail);
        }

        const shareLinkBtn = overlay.querySelector('#share-copy-link-btn');
        if (shareLinkBtn) {
            shareLinkBtn.addEventListener('click', copySignupLink);
        }

        // Keyboard navigation
        document.addEventListener('keydown', handleKeyDown);
    }

    function handleKeyDown(e) {
        if (e.key === 'Escape' && isModalInitialized && !isClosing) {
            closeModal();
        }
    }

    async function copyReferralCode() {
        const codeText = document.getElementById('referral-code-text');
        const copyButton = document.getElementById('copy-referral-code-btn');
        
        if (codeText && copyButton) {
            try {
                await navigator.clipboard.writeText(codeText.textContent);
                
                // Show success feedback
                const copyIcon = copyButton.querySelector('.copy-icon');
                const checkIcon = copyButton.querySelector('.check-icon');
                
                copyIcon.classList.add('hidden');
                checkIcon.classList.remove('hidden');
                
                setTimeout(() => {
                    copyIcon.classList.remove('hidden');
                    checkIcon.classList.add('hidden');
                }, 2000);
                
                if (typeof showNotification === 'function') {
                    showNotification('Referral code copied to clipboard!', 'success');
                }
            } catch (err) {
                console.error('Failed to copy referral code:', err);
                if (typeof showNotification === 'function') {
                    showNotification('Failed to copy referral code. Please try again.', 'error');
                }
            }
        }
    }

    function shareViaEmail() {
        const referralCode = currentUserData?.referralCode || '';
        const subject = encodeURIComponent('Join me on SkillsAssess!');
        const body = encodeURIComponent(
            `Hi!\n\nI've been using SkillsAssess for skills assessment and training, and I think you'd find it valuable too.\n\n` +
            `Use my referral code "${referralCode}" when you sign up and we'll both get 5 bonus credits!\n\n` +
            `Sign up here: https://dashboard.skillsassess.ai/signup.html\n\n` +
            `Best regards`
        );
        
        window.open(`mailto:?subject=${subject}&body=${body}`);
    }

    function copySignupLink() {
        const referralCode = currentUserData?.referralCode || '';
        const signupLink = `https://dashboard.skillsassess.ai/signup.html?ref=${referralCode}`;
        
        navigator.clipboard.writeText(signupLink).then(() => {
            if (typeof showNotification === 'function') {
                showNotification('Signup link copied to clipboard!', 'success');
            }
        }).catch(err => {
            console.error('Failed to copy signup link:', err);
            if (typeof showNotification === 'function') {
                showNotification('Failed to copy signup link. Please try again.', 'error');
            }
        });
    }

    function closeModal() {
        if (isClosing) return;
        isClosing = true;

        const overlay = document.getElementById('referral-modal-overlay');
        if (overlay) {
            const modalContent = overlay.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.opacity = '0';
                modalContent.style.transform = 'scale(0.95)';
            }

            overlay.style.opacity = '0';

            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
                isModalInitialized = false;
                isClosing = false;
                document.removeEventListener('keydown', handleKeyDown);
            }, 300);
        }
    }

    function addStyles() {
        if (document.getElementById('referral-modal-styles')) return;

        const styleSheet = document.createElement('style');
        styleSheet.id = 'referral-modal-styles';
        styleSheet.textContent = `
            /* Referral modal specific styles - using existing modal base styles */
            #referral-modal-overlay .modal-content {
                max-width: 600px;
                font-family: system-ui, -apple-system, sans-serif;
                color: #374151;
            }

            .referral-modal-subtitle {
                font-size: 0.875rem;
                color: #6b7280;
                margin: 0.25rem 0 0 0;
            }

            .referral-modal-body h3 {
                font-size: 1.125rem;
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 1rem 0;
            }

            .referral-how-it-works {
                margin-bottom: 2rem;
            }

            .referral-steps {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .referral-step {
                display: flex;
                align-items: flex-start;
                gap: 1rem;
            }

            .referral-step-number {
                background: #3b82f6;
                color: white;
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                font-size: 0.875rem;
                flex-shrink: 0;
            }

            .referral-step-content h4 {
                font-size: 1rem;
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 0.25rem 0;
            }

            .referral-step-content p {
                font-size: 0.875rem;
                color: #6b7280;
                margin: 0;
            }

            .referral-code-section {
                margin-bottom: 2rem;
            }

            .referral-code-container {
                background: #f9fafb;
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                padding: 1rem;
            }

            .referral-code-display {
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: white;
                border: 1px solid #d1d5db;
                border-radius: 0.375rem;
                padding: 0.75rem;
                margin-bottom: 0.5rem;
            }

            .referral-code-text {
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 1.125rem;
                font-weight: 600;
                color: #1f2937;
                letter-spacing: 0.05em;
            }

            .referral-copy-button {
                background: #f3f4f6;
                border: 1px solid #d1d5db;
                border-radius: 0.375rem;
                padding: 0.5rem;
                cursor: pointer;
                transition: all 0.2s ease;
                color: #6b7280;
            }

            .referral-copy-button:hover {
                background: #e5e7eb;
                color: #374151;
            }

            .referral-code-help {
                font-size: 0.75rem;
                color: #6b7280;
                margin: 0;
            }

            .referral-stats-section {
                margin-bottom: 2rem;
            }

            .referral-stats-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }

            .referral-stat-card {
                background: #f9fafb;
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                padding: 1rem;
                text-align: center;
            }

            .referral-stat-number {
                font-size: 2rem;
                font-weight: 700;
                color: #3b82f6;
                margin-bottom: 0.25rem;
            }

            .referral-stat-label {
                font-size: 0.875rem;
                color: #6b7280;
            }

            .referral-sharing-section {
                margin-bottom: 1rem;
            }

            .referral-sharing-text {
                font-size: 0.875rem;
                color: #6b7280;
                margin-bottom: 1rem;
                line-height: 1.5;
            }

            .referral-sharing-buttons {
                display: flex;
                gap: 0.75rem;
                flex-wrap: wrap;
            }

            .referral-share-button {
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 0.375rem;
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .referral-share-button:hover {
                background: #2563eb;
            }

            .hidden {
                display: none;
            }

            /* Responsive design for referral modal content */
            @media (max-width: 640px) {
                .referral-steps {
                    gap: 0.75rem;
                }

                .referral-stats-grid {
                    grid-template-columns: 1fr;
                }

                .referral-sharing-buttons {
                    flex-direction: column;
                }

                .referral-share-button {
                    justify-content: center;
                }
            }
        `;
        
        document.head.appendChild(styleSheet);
    }

    // Expose the function globally
    global.showReferralModal = showReferralModal;

})(window);
